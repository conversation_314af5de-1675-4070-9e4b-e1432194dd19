/**
 * @file    softspi.c
 * <AUTHOR>
 * @version 0.2
 * @date    2021.10.12
 * @brief   STM32 SoftSPI Library
 */

#include "softspi.h"

#define SCLK_Set HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx-><PERSON><PERSON><PERSON>_Pin, GPIO_PIN_SET)
#define SCLK_Clr HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_PIN_RESET)

#define MOSI_Set HAL_GPIO_WritePin(SoftSPIx->MOSI_GPIO, SoftSPIx->MOSI_Pin, GPIO_PIN_SET)
#define MOSI_Clr HAL_GPIO_WritePin(SoftSPIx->MOSI_GPIO, SoftSPIx->MOSI_Pin, GPIO_PIN_RESET)

#define MISO_Read HAL_GPIO_ReadPin(SoftSPIx->MISO_GPIO, SoftSPIx->MISO_Pin)

#define SS_Set HAL_GPIO_WritePin(SoftSPIx->SS_GPIO, SoftSPIx->SS_Pin, GPIO_PIN_SET)
#define SS_Clr HAL_GPIO_WritePin(SoftSPIx->SS_GPIO, SoftSPIx->SS_Pin, GPIO_PIN_RESET)

#define Delay SoftSPI_Delay_us(SoftSPIx->Delay_Time)

uint8_t SoftSPI_GPIOx_Pin_Init(GPIO_TypeDef *GPIOx, uint32_t Pin, uint32_t Mode, uint32_t Pull);

HAL_StatusTypeDef SoftSPI_Init(SoftSPI_TypeDef *SoftSPIx)
{
    SoftSPIx->SCLK_GPIO = SoftSPIx->SCLK_GPIO;
    SoftSPIx->SCLK_Pin = SoftSPIx->SCLK_Pin;

    if (!SoftSPI_GPIOx_Pin_Init(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL))
        return HAL_ERROR;

    SoftSPIx->MOSI_GPIO = SoftSPIx->MOSI_GPIO;
    SoftSPIx->MOSI_Pin = SoftSPIx->MOSI_Pin;

    if (!SoftSPI_GPIOx_Pin_Init(SoftSPIx->MOSI_GPIO, SoftSPIx->MOSI_Pin, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL))
        return HAL_ERROR;

    SoftSPIx->MISO_GPIO = SoftSPIx->MISO_GPIO;
    SoftSPIx->MISO_Pin = SoftSPIx->MISO_Pin;

    if (!SoftSPI_GPIOx_Pin_Init(SoftSPIx->MISO_GPIO, SoftSPIx->MISO_Pin, GPIO_MODE_INPUT, GPIO_PULLUP))
        return HAL_ERROR;

    SoftSPIx->SS_GPIO = SoftSPIx->SS_GPIO;
    SoftSPIx->SS_Pin = SoftSPIx->SS_Pin;

    if (!SoftSPI_GPIOx_Pin_Init(SoftSPIx->SS_GPIO, SoftSPIx->SS_Pin, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL))
        return HAL_ERROR;

    SoftSPIx->Delay_Time = SoftSPIx->Delay_Time;
		SCLK_Clr;
    return HAL_OK;
}

void SoftSPI_SetSS(SoftSPI_TypeDef *SoftSPIx)
{
    SS_Set;
}

void SoftSPI_ClrSS(SoftSPI_TypeDef *SoftSPIx)
{
    SS_Clr;
}

uint8_t SoftSPI_WriteRead(SoftSPI_TypeDef *SoftSPIx, uint8_t byte)
{
    uint8_t data = 0;
    uint8_t i;

    for (i = 0; i < 8; i++)
    {
        SCLK_Clr;
        Delay;

        if (byte & 0x80)
            MOSI_Set;
        else
            MOSI_Clr;

        Delay;

        byte <<= 1;
        SCLK_Set;
        Delay;

        data <<= 1;
        if (MISO_Read == GPIO_PIN_SET)
            data |= 0x01;

        Delay;
    }
    return data;
}
uint8_t SoftSPI_WriteRead_four(SoftSPI_TypeDef *SoftSPIx, uint8_t byte)
{
    uint8_t data = 0;
    uint8_t i;
//		SCLK_Set;
    Delay;
    Delay;	
    for (i = 0; i < 4; i++)
    {
        SCLK_Clr;
        Delay;

        if (byte & 0x80)
            MOSI_Set;
        else
            MOSI_Clr;

        Delay;

        byte <<= 1;
        SCLK_Set;
        Delay;

        data <<= 1;
        if (MISO_Read == GPIO_PIN_SET)
            data |= 0x01;

        Delay;
    }
		SCLK_Clr;
    return data;
}
void SoftSPI_WriteReadBuff(SoftSPI_TypeDef *SoftSPIx, uint8_t *pWrite, uint8_t *pRead, uint32_t len)
{
    uint8_t data;
    uint8_t byte;
    uint8_t i, j;

    for (j = 0; j < len; j++)
    {
        data = 0;
        byte = pWrite[j];

        for (i = 0; i < 8; i++)
        {
            SCLK_Clr;
            Delay;

            if (byte & 0x80)
                MOSI_Set;
            else
                MOSI_Clr;

            Delay;

            byte <<= 1;
            SCLK_Set;
            Delay;

            data <<= 1;
            if (MISO_Read == GPIO_PIN_SET)
                data |= 0x01;

            Delay;
        }

        pRead[j] = data;
    }

}

uint8_t SoftSPI_GPIOx_Pin_Init(GPIO_TypeDef *GPIOx, uint32_t Pin, uint32_t Mode, uint32_t Pull)
{
    switch ((uint32_t)(GPIOx))
    {
    case (uint32_t)GPIOA:
    {
        GPIO_InitTypeDef GPIO_Initure;
        __HAL_RCC_GPIOA_CLK_ENABLE();

        GPIO_Initure.Pin = Pin;
        GPIO_Initure.Mode = Mode;
        GPIO_Initure.Pull = Pull;
        GPIO_Initure.Speed = GPIO_SPEED_FREQ_HIGH;
        HAL_GPIO_Init(GPIOA, &GPIO_Initure);
    }
    break;

    case (uint32_t)GPIOB:
    {
        GPIO_InitTypeDef GPIO_Initure;
        __HAL_RCC_GPIOB_CLK_ENABLE();

        GPIO_Initure.Pin = Pin;
        GPIO_Initure.Mode = Mode;
        GPIO_Initure.Pull = Pull;
        GPIO_Initure.Speed = GPIO_SPEED_FREQ_HIGH;
        HAL_GPIO_Init(GPIOB, &GPIO_Initure);
    }
    break;

    case (uint32_t)GPIOC:
    {
        GPIO_InitTypeDef GPIO_Initure;
        __HAL_RCC_GPIOC_CLK_ENABLE();

        GPIO_Initure.Pin = Pin;
        GPIO_Initure.Mode = Mode;
        GPIO_Initure.Pull = Pull;
        GPIO_Initure.Speed = GPIO_SPEED_FREQ_HIGH;
        HAL_GPIO_Init(GPIOC, &GPIO_Initure);
    }
    break;

    case (uint32_t)GPIOD:
    {
        GPIO_InitTypeDef GPIO_Initure;
        __HAL_RCC_GPIOD_CLK_ENABLE();

        GPIO_Initure.Pin = Pin;
        GPIO_Initure.Mode = Mode;
        GPIO_Initure.Pull = Pull;
        GPIO_Initure.Speed = GPIO_SPEED_FREQ_HIGH;
        HAL_GPIO_Init(GPIOD, &GPIO_Initure);
    }
    break;

    default:
        return 0;
    }

    return 1;
}

//����Short֡
void SendShortFrame(SoftSPI_TypeDef *SoftSPIx, uint8_t rw, uint16_t address, uint16_t data)
{
    uint32_t frame = 0;
    frame |= ((uint32_t)0 << 31); // Mode: 1 bits
    frame |= ((uint32_t)1 << 30); // Mode: 1 bits
    frame |= ((uint32_t)0 << 29); // Mode: 1 bits	
    frame |= ((uint32_t)rw << 28);   // R/W: 1 bit
    frame |= (0 << 27);              // OP: 1 bit
    frame |= ((uint32_t)address << 16); // Address[10:0]: 11 bits
    frame |= (data & 0xFFFF);        // DATA[15:0]: 16 bits

    uint8_t bytes[4];
    bytes[0] = (frame >> 24) & 0xFF;
    bytes[1] = (frame >> 16) & 0xFF;
    bytes[2] = (frame >> 8) & 0xFF;
    bytes[3] = frame & 0xFF;

    SoftSPI_ClrSS(SoftSPIx);

    for(int i = 0; i < 4; i++)
    {
        SoftSPI_WriteRead(SoftSPIx, bytes[i]);
    }
    SoftSPI_SetSS(SoftSPIx);
		SCLK_Set;
		Delay_us(2);	
    SoftSPI_WriteRead_four(SoftSPIx, 0x55);
		Delay_us(2);		
    SoftSPI_ClrSS(SoftSPIx);
		Delay_us(10);		
    SoftSPI_SetSS(SoftSPIx);		
}
//����Long֡����
void SendLongFrame(SoftSPI_TypeDef *SoftSPIx, uint8_t rw, uint16_t address, uint8_t *data)
{
    // ����16λ����֡
    uint16_t command = 0;
    command |= (0x2 << 13);          // Mode: 010
    command |= ((rw & 0x1) << 12);   // R/W: 1 bit
    command |= (0 << 11);            // OP: 1 bit (�̶�Ϊ0)
    command |= (address & 0x7FF);    // Address[10:0]: 11 bits
    
    // �ܹ�248λ = 31�ֽ�
    uint8_t frame[31];
    
    // ǰ2�ֽ�������֡
    frame[0] = (command >> 8) & 0xFF;
    frame[1] = command & 0xFF;
    
    // ��29�ֽ���232λ����
    for(int i = 0; i < 29; i++)
    {
        frame[i + 2] = data[i];
    }
    
    // ��һ��CS������ - ��������֡
    SoftSPI_ClrSS(SoftSPIx);
    
    for(int i = 0; i < 31; i++)
    {
        SoftSPI_WriteRead(SoftSPIx, frame[i]);
    }
        
    SoftSPI_SetSS(SoftSPIx);
		SCLK_Set;
		Delay_us(2);	
    SoftSPI_WriteRead_four(SoftSPIx, 0x55);
		Delay_us(2);		
    SoftSPI_ClrSS(SoftSPIx);
		Delay_us(10);		
    SoftSPI_SetSS(SoftSPIx);
}
//дShort֡����
void ARW9679_WriteShortFrame(SoftSPI_TypeDef *SoftSPIx, uint16_t address, uint16_t data)
{
    SendShortFrame(SoftSPIx, SPI_WRITE_CMD, address, data);
}

//��Short֡����
uint16_t ARW9679_ReadShortFrame(SoftSPI_TypeDef *SoftSPIx, uint16_t address)
{
    uint32_t frame = 0;
    uint16_t read_data = 0;
	
    frame |= ((uint32_t)0 << 31); // Mode: 1 bits
    frame |= ((uint32_t)1 << 30); // Mode: 1 bits
    frame |= ((uint32_t)0 << 29); // Mode: 1 bits	
    frame |= ((uint32_t)1 << 28);   // R/W: 1 bit
    frame |= (0 << 27);              // OP: 1 bit
    frame |= ((uint32_t)address << 16); // Address[10:0]: 11 bits
    frame |= (0x55 & 0xFFFF);        // DATA[15:0]: 16 bits

    uint8_t bytes[4];
    bytes[0] = (frame >> 24) & 0xFF;
    bytes[1] = (frame >> 16) & 0xFF;
    bytes[2] = (frame >> 8) & 0xFF;
    bytes[3] = frame & 0xFF;

    SoftSPI_ClrSS(SoftSPIx);

    for(int i = 0; i < 4; i++)
    {
      read_data = SoftSPI_WriteRead(SoftSPIx, bytes[i]);
    }
    SoftSPI_SetSS(SoftSPIx);
		SCLK_Set;
		Delay_us(2);	
    SoftSPI_WriteRead_four(SoftSPIx, 0x55);
		Delay_us(2);		
    SoftSPI_ClrSS(SoftSPIx);
		Delay_us(10);		
    SoftSPI_SetSS(SoftSPIx);			
//    // ��һ�������Ͷ�ȡ����֡ (��n֡)
//    SendShortFrame(SoftSPIx, SPI_READ_CMD, address, 0x0000);
//    
//    // �ڶ�������������֡�Ի�ȡ��n֡�Ļض����� (��n+1֡)
//    // ���ݶ������淶����n+1֡��SDO��MODE��R/W��OP��AddressΪ0��DATAΪ�ض�����
//    uint32_t dummy_frame = 0x00000000; // ����ȫ0֡
//    uint8_t frame_bytes[4];
//    uint16_t read_data = 0;
//    
//    // ��32λdummy֡�ֽ�Ϊ4���ֽ�
//    frame_bytes[0] = (dummy_frame >> 24) & 0xFF;
//    frame_bytes[1] = (dummy_frame >> 16) & 0xFF;
//    frame_bytes[2] = (dummy_frame >> 8) & 0xFF;
//    frame_bytes[3] = dummy_frame & 0xFF;
//    
//    // ����SS�źſ�ʼ�ڶ��δ���
//    SoftSPI_ClrSS(SoftSPIx);
//    
//    // ����4���ֽڲ����ջض�����
//    for(int i = 0; i < 4; i++)
//    {
//        uint8_t received = SoftSPI_WriteRead(SoftSPIx, frame_bytes[i]);
//        // ���ݶ������淶��DATA����(�������ֽ�)�����ض�����
//        if(i == 2)
//        {
//            read_data = (uint16_t)received << 8;
//        }
//        else if(i == 3)
//        {
//            read_data |= received;
//        }
//    }
//    
//    // ����SS�ź� (��һ��������)
//    SoftSPI_SetSS(SoftSPIx);
//    
//    // �ȴ�4��ʱ������
//    for(int i = 0; i < 4; i++)
//    {
//        HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_PIN_RESET);
//        Delay_us(SoftSPIx->Delay_Time);
//        HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_PIN_SET);
//        Delay_us(SoftSPIx->Delay_Time);
//    }
//    
//    // ����������SS�ź� (�ڶ���������)
//    SoftSPI_ClrSS(SoftSPIx);
//    Delay_us(SoftSPIx->Delay_Time);
//    SoftSPI_SetSS(SoftSPIx);
//    
    return read_data;
}
//дLong֡����
void ARW9679_WriteLongFrame(SoftSPI_TypeDef *SoftSPIx, uint16_t address, uint8_t *data)
{
    SendLongFrame(SoftSPIx, SPI_WRITE_CMD, address, data);
}
//��Long֡����
void ARW9679_ReadLongFrame(SoftSPI_TypeDef *SoftSPIx, uint16_t address, uint8_t *data)
{
    // ����16λ����֡
    uint16_t command = 0;
    command |= (0x2 << 13);          // Mode: 010
    command |= ((1 & 0x1) << 12);   // R/W: 1 bit
    command |= (0 << 11);            // OP: 1 bit (�̶�Ϊ0)
    command |= (address & 0x7FF);    // Address[10:0]: 11 bits
    
    // �ܹ�248λ = 31�ֽ�
    uint8_t frame[31];
    
    // ǰ2�ֽ�������֡
    frame[0] = (command >> 8) & 0xFF;
    frame[1] = command & 0xFF;
    
    // ��29�ֽ���232λ����
    for(int i = 0; i < 29; i++)
    {
        frame[i + 2] = data[i];
    }
    
    // ��һ��CS������ - ��������֡
    SoftSPI_ClrSS(SoftSPIx);
    
    for(int i = 0; i < 31; i++)
    {
     data[i] = SoftSPI_WriteRead(SoftSPIx, frame[i]);
    }
        
    SoftSPI_SetSS(SoftSPIx);
		SCLK_Set;
		Delay_us(2);	
    SoftSPI_WriteRead_four(SoftSPIx, 0x55);
		Delay_us(2);		
    SoftSPI_ClrSS(SoftSPIx);
		Delay_us(10);		
    SoftSPI_SetSS(SoftSPIx);	
//    // ��һ��������Long֡��ȡ���� (��n֡)
//    uint8_t dummy_data[29] = {0}; // ����ȫ0����
//    SendLongFrame(SoftSPIx, SPI_READ_CMD, address, dummy_data);
//    
//    // �ڶ�������������Long֡�Ի�ȡ��n֡�Ļض����� (��n+1֡)
//    // ���ݶ������淶����n+1֡��SDO�������Ϊ0�����ݲ���Ϊ�ض�����
//    uint16_t dummy_cmd = 0x0000; // ����ȫ0����
//    uint8_t cmd_bytes[2];
//    
//    // ��16λdummy����֡�ֽ�Ϊ2���ֽ�
//    cmd_bytes[0] = (dummy_cmd >> 8) & 0xFF;
//    cmd_bytes[1] = dummy_cmd & 0xFF;
//    
//    // ����SS�źſ�ʼ�ڶ��δ���
//    SoftSPI_ClrSS(SoftSPIx);
//    
//    // ����2�ֽ�dummy����֡
//    SoftSPI_WriteRead(SoftSPIx, cmd_bytes[0]);
//    SoftSPI_WriteRead(SoftSPIx, cmd_bytes[1]);
//    
//    // ����232λ�ض����� (29�ֽ�)
//    for(int i = 0; i < 29; i++)
//    {
//        data[i] = SoftSPI_WriteRead(SoftSPIx, 0x00); // ����0x00�����ջض�����
//    }
//    
//    // ����SS�ź� (��һ��������)
//    SoftSPI_SetSS(SoftSPIx);
//    
//    // �ȴ�4��ʱ������
//    for(int i = 0; i < 4; i++)
//    {
//        HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_PIN_RESET);
//        Delay_us(SoftSPIx->Delay_Time);
//        HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_PIN_SET);
//        Delay_us(SoftSPIx->Delay_Time);
//    }
//    
//    // ����������SS�ź� (�ڶ���������)
//    SoftSPI_ClrSS(SoftSPIx);
//    Delay_us(SoftSPIx->Delay_Time);
//    SoftSPI_SetSS(SoftSPIx);
}
//��λ
void ARW9679_TimingReset(SoftSPI_TypeDef *SoftSPIx)
{
    // ��CS��Ч�ڼ䣬����1��5��ʱ��������и�λ
    SoftSPI_ClrSS(SoftSPIx);
    
    // ����3��ʱ������
    for(int i = 0; i < 3; i++)
    {
        // �ֶ�����ʱ������
        HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_PIN_RESET);
        Delay_us(SoftSPIx->Delay_Time);
        HAL_GPIO_WritePin(SoftSPIx->SCLK_GPIO, SoftSPIx->SCLK_Pin, GPIO_PIN_SET);
        Delay_us(SoftSPIx->Delay_Time);
    }
    
    SoftSPI_SetSS(SoftSPIx);
    Delay_us(100); // �ȴ���λ���
}
//��ʼ��
void ARW9679_Initialize(SoftSPI_TypeDef *SoftSPIx)
{
    // ��ʼ���������ݱ�
    typedef struct {
        uint16_t address;
        uint16_t data;
    } ARW9679_InitData_t;
    
    ARW9679_InitData_t init_sequence[] = {
        {0x000, 0x0200},  // No.1
        {0x000, 0x0000},  // No.2
        {0x023, 0x4924},  // No.3
        {0x024, 0x0401},  // No.4
        {0x082, 0x88F0},  // No.5
        {0x028, 0x0004},  // No.6
        {0x029, 0x8CF3},  // No.7
        {0x02A, 0x9044},  // No.8
        {0x080, 0x4000}   // No.9
    };
    
    uint8_t init_count = sizeof(init_sequence) / sizeof(ARW9679_InitData_t);
    
    // ִ�г�ʼ������
    for(uint8_t i = 0; i < init_count; i++)
    {
        SendShortFrame(SoftSPIx, SPI_WRITE_CMD, init_sequence[i].address, init_sequence[i].data);
        HAL_Delay(10); // ÿ�����ü��10ms
    }
}
//�л���Long֡ģʽ
void ARW9679_SwitchToLongFrame(SoftSPI_TypeDef *SoftSPIx)
{
    uint32_t frame = 0;
    frame |= ((uint32_t)0 << 31); // Mode: 1 bits
    frame |= ((uint32_t)1 << 30); // Mode: 1 bits
    frame |= ((uint32_t)0 << 29); // Mode: 1 bits	
    frame |= ((uint32_t)0 << 28);   // R/W: 1 bit
    frame |= (1 << 27);              // OP: 1 bit
    frame |= ((uint32_t)0x0009 << 16); // Address[10:0]: 11 bits
    frame |= (0x0004 & 0xFFFF);        // DATA[15:0]: 16 bits

    uint8_t bytes[4];
    bytes[0] = (frame >> 24) & 0xFF;
    bytes[1] = (frame >> 16) & 0xFF;
    bytes[2] = (frame >> 8) & 0xFF;
    bytes[3] = frame & 0xFF;

    SoftSPI_ClrSS(SoftSPIx);

    for(int i = 0; i < 4; i++)
    {
        SoftSPI_WriteRead(SoftSPIx, bytes[i]);
    }
    SoftSPI_SetSS(SoftSPIx);
		SCLK_Set;
		Delay_us(2);	
    SoftSPI_WriteRead_four(SoftSPIx, 0x55);
		Delay_us(2);		
    SoftSPI_ClrSS(SoftSPIx);
		Delay_us(10);		
    SoftSPI_SetSS(SoftSPIx);	}

//�л���Short֡ģʽ
void ARW9679_SwitchToShortFrame(SoftSPI_TypeDef *SoftSPIx)
{
    // ����16λ����֡
    uint16_t command = 0;
    command |= (0x2 << 13);          // Mode: 010
    command |= ((0 & 0x1) << 12);   // R/W: 1 bit
    command |= (1 << 11);            // OP: 1 bit (�̶�Ϊ0)
    command |= (0x0009 & 0x7FF);    // Address[10:0]: 11 bits
    
    // �ܹ�248λ = 31�ֽ�
    uint8_t frame[31];
    
    // ǰ2�ֽ�������֡
    frame[0] = (command >> 8) & 0xFF;
    frame[1] = command & 0xFF;
    
    // ��29�ֽ���232λ����
    for(int i = 0; i < 29; i++)
    {
        frame[i + 2] = 0x00;
    }
    
    // ��һ��CS������ - ��������֡
    SoftSPI_ClrSS(SoftSPIx);
    
    for(int i = 0; i < 31; i++)
    {
        SoftSPI_WriteRead(SoftSPIx, frame[i]);
    }
        
    SoftSPI_SetSS(SoftSPIx);
		SCLK_Set;
		Delay_us(2);	
    SoftSPI_WriteRead_four(SoftSPIx, 0x55);
		Delay_us(2);		
    SoftSPI_ClrSS(SoftSPIx);
		Delay_us(10);		
    SoftSPI_SetSS(SoftSPIx);}
// �¶�
int16_t ARW9679_ConvertTemperature(uint8_t temp_code)
{
    // �¶�����¶�ֵ��Ӧ��ϵ��
    typedef struct {
        uint8_t code;
        int16_t temp;
    } temp_table_t;
    
    static const temp_table_t temp_table[] = {
        {0x1E, -55}, {0x23, -50}, {0x27, -45}, {0x2B, -40}, {0x31, -35},
        {0x36, -30}, {0x3C, -25}, {0x42, -20}, {0x48, -15}, {0x4D, -10},
        {0x54, -5},  {0x5A, 0},   {0x5F, 5},   {0x65, 10},  {0x6B, 15},
        {0x71, 20},  {0x77, 25},  {0x7D, 30},  {0x82, 35},  {0x89, 40},
        {0x8F, 45},  {0x96, 50},  {0x9B, 55},  {0xA2, 60},  {0xA7, 65},
        {0xAF, 70},  {0xB5, 75},  {0xBA, 80},  {0xC2, 85},  {0xC9, 90},
        {0xCF, 95},  {0xD6, 100}, {0xDD, 105}
    };
    
    const int table_size = sizeof(temp_table)/sizeof(temp_table[0]);
    
    // ���ȼ���Ƿ�Ϊ��ȷƥ��
    for(int i = 0; i < table_size; i++)
    {
        if(temp_table[i].code == temp_code)
        {
            return temp_table[i].temp;
        }
    }
    
    // ������ӽ����¶�ֵ���������Բ�ֵ
    // ����Ƿ񳬳���Χ
    if(temp_code < temp_table[0].code)
    {
        // �¶���С����Сֵ����������¶�
        return temp_table[0].temp;
    }
    if(temp_code > temp_table[table_size-1].code)
    {
        // �¶���������ֵ����������¶�
        return temp_table[table_size-1].temp;
    }
    
    // �ڱ��в��Ҳ�ֵ����
    for(int i = 0; i < table_size - 1; i++)
    {
        if(temp_code > temp_table[i].code && temp_code < temp_table[i+1].code)
        {
            // �ҵ���ֵ���䣬�������Բ�ֵ
            uint8_t code1 = temp_table[i].code;
            uint8_t code2 = temp_table[i+1].code;
            int16_t temp1 = temp_table[i].temp;
            int16_t temp2 = temp_table[i+1].temp;
            
            // ���Բ�ֵ��ʽ��temp = temp1 + (temp_code - code1) * (temp2 - temp1) / (code2 - code1)
            int16_t interpolated_temp = temp1 + ((int32_t)(temp_code - code1) * (temp2 - temp1)) / (code2 - code1);
            
            return interpolated_temp;
        }
    }
    
    // �����ϲ�Ӧ�õ�������
    return -999;
}
//��ȡARW9679�¶ȼĴ���
uint16_t ARW9679_ReadTemperature(SoftSPI_TypeDef *SoftSPIx)
{
    return ARW9679_ReadShortFrame(SoftSPIx, ARW9679_REG_TEMP);
}
int16_t ARW9679_ReadTemperatureDirect(SoftSPI_TypeDef *SoftSPIx)
{
    // ��ȡ�¶ȼĴ���
    uint16_t temp_reg = ARW9679_ReadTemperature(SoftSPIx);
    
    // ��ȡ��8λ�¶��� (DATAλ����8λ��Ч)
    uint8_t temp_code = temp_reg & 0xFF;
    
    // ת���¶���Ϊʵ���¶�ֵ
    return ARW9679_ConvertTemperature(temp_code);
}

/**
 * @brief TDDģʽ�Ĵ�����ʼ��
 * 
 * ��ʼ������TDD_G*�Ĵ���ΪĬ��ֵ��֧������TDDģʽ���ã�
 * - TDD_G1: [TDD_H,TDD_V]=[0,0] ģʽ����
 * - TDD_G2: [TDD_H,TDD_V]=[0,1] ģʽ����
 * - TDD_G3: [TDD_H,TDD_V]=[1,0] ģʽ����
 * - TDD_G4: [TDD_H,TDD_V]=[1,1] ģʽ����
 * 
 * @param SoftSPIx SoftSPIʵ��ָ��
 * @retval None
 */
void ARW9679_TDD_Initialize(SoftSPI_TypeDef *SoftSPIx)
{
    // ��ʼ��TDD Group 1 �Ĵ���
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G1_A, ARW9679_TDD_G1_A_DEFAULT);
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G1_B, ARW9679_TDD_G1_B_DEFAULT);
    
    // ��ʼ��TDD Group 2 �Ĵ���
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G2_A, ARW9679_TDD_G2_A_DEFAULT);
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G2_B, ARW9679_TDD_G2_B_DEFAULT);
    
    // ��ʼ��TDD Group 3 �Ĵ���
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G3_A, ARW9679_TDD_G3_A_DEFAULT);
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G3_B, ARW9679_TDD_G3_B_DEFAULT);
    
    // ��ʼ��TDD Group 4 �Ĵ���
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G4_A, ARW9679_TDD_G4_A_DEFAULT);
    ARW9679_WriteShortFrame(SoftSPIx, ARW9679_REG_TDD_G4_B, ARW9679_TDD_G4_B_DEFAULT);
}

/**
 * @brief ����ָ��TDD��ļĴ���
 * 
 * @param SoftSPIx SoftSPIʵ��ָ��
 * @param group_num TDD��� (1-4)
 * @param reg_a_value TDD_G*_A�Ĵ���ֵ (H1-H8 & V1-V8: ON/OFF)
 * @param reg_b_value TDD_G*_B�Ĵ���ֵ (H_T/R��V_T/R)
 * @retval None
 */
void ARW9679_TDD_ConfigGroup(SoftSPI_TypeDef *SoftSPIx, uint8_t group_num, uint16_t reg_a_value, uint16_t reg_b_value)
{
    uint16_t reg_a_addr, reg_b_addr;
    
    // �������ȷ���Ĵ�����ַ
    switch(group_num)
    {
        case 1:
            reg_a_addr = ARW9679_REG_TDD_G1_A;
            reg_b_addr = ARW9679_REG_TDD_G1_B;
            break;
        case 2:
            reg_a_addr = ARW9679_REG_TDD_G2_A;
            reg_b_addr = ARW9679_REG_TDD_G2_B;
            break;
        case 3:
            reg_a_addr = ARW9679_REG_TDD_G3_A;
            reg_b_addr = ARW9679_REG_TDD_G3_B;
            break;
        case 4:
            reg_a_addr = ARW9679_REG_TDD_G4_A;
            reg_b_addr = ARW9679_REG_TDD_G4_B;
            break;
        default:
            // ��Ч����ţ�ֱ�ӷ���
            return;
    }
    
    // д��Ĵ���ֵ
    ARW9679_WriteShortFrame(SoftSPIx, reg_a_addr, reg_a_value);
    ARW9679_WriteShortFrame(SoftSPIx, reg_b_addr, reg_b_value);
}

/**
 * @brief ��ȡָ��TDD��ļĴ���ֵ
 * 
 * @param SoftSPIx SoftSPIʵ��ָ��
 * @param group_num TDD��� (1-4)
 * @param reg_a_value ���ڴ洢TDD_G*_A�Ĵ���ֵ��ָ��
 * @param reg_b_value ���ڴ洢TDD_G*_B�Ĵ���ֵ��ָ��
 * @retval None
 */
void ARW9679_TDD_ReadGroup(SoftSPI_TypeDef *SoftSPIx, uint8_t group_num, uint16_t *reg_a_value, uint16_t *reg_b_value)
{
    uint16_t reg_a_addr, reg_b_addr;
    
    // ���ָ����Ч��
    if (reg_a_value == NULL || reg_b_value == NULL)
    {
        return;
    }
    
    // �������ȷ���Ĵ�����ַ
    switch(group_num)
    {
        case 1:
            reg_a_addr = ARW9679_REG_TDD_G1_A;
            reg_b_addr = ARW9679_REG_TDD_G1_B;
            break;
        case 2:
            reg_a_addr = ARW9679_REG_TDD_G2_A;
            reg_b_addr = ARW9679_REG_TDD_G2_B;
            break;
        case 3:
            reg_a_addr = ARW9679_REG_TDD_G3_A;
            reg_b_addr = ARW9679_REG_TDD_G3_B;
            break;
        case 4:
            reg_a_addr = ARW9679_REG_TDD_G4_A;
            reg_b_addr = ARW9679_REG_TDD_G4_B;
            break;
        default:
            // ��Ч����ţ�����Ϊ0������
            *reg_a_value = 0;
            *reg_b_value = 0;
            return;
    }
    
    // ��ȡ�Ĵ���ֵ
    *reg_a_value = ARW9679_ReadShortFrame(SoftSPIx, reg_a_addr);
    *reg_b_value = ARW9679_ReadShortFrame(SoftSPIx, reg_b_addr);
}

