/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "spi.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "softspi.h"
#include <string.h>
#include <stdlib.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// 串口命令解析相关变量
#define UART_RX_BUFFER_SIZE 128
#define UART_CMD_BUFFER_SIZE 128
uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];
uint8_t uart_cmd_buffer[UART_CMD_BUFFER_SIZE];
volatile uint8_t uart_rx_index = 0;
volatile uint8_t uart_cmd_ready = 0;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void ProcessUARTCommand(char* cmd);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
void Delay_us(uint32_t us)
{
    uint32_t cycles = us * (SystemCoreClock / 1000000); // 计算循环次数
    while (cycles--) {
        __NOP(); // 执行空操作（防止被编译器优化）
    }
}

SoftSPI_TypeDef SoftSPI1 = {
    .SCLK_GPIO = GPIOA,
    .SCLK_Pin = GPIO_PIN_1,
    .MOSI_GPIO = GPIOA,
    .MOSI_Pin = GPIO_PIN_2,
    .MISO_GPIO = GPIOA,
    .MISO_Pin = GPIO_PIN_3,
    .SS_GPIO = GPIOA,
    .SS_Pin = GPIO_PIN_0,
    .Delay_Time = 1 
};

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_SPI1_Init();
  MX_USART1_UART_Init();
  MX_TIM2_Init();
  /* USER CODE BEGIN 2 */
  if (HAL_OK == SoftSPI_Init(&SoftSPI1))
  {
//    // 执行ARW9679时序复位
//    ARW9679_TimingReset(&SoftSPI1);
//    
//    // 执行ARW9679芯片初始化
//    ARW9679_Initialize(&SoftSPI1);
//    
//    // 执行TDD模式寄存器初始化
//    ARW9679_TDD_Initialize(&SoftSPI1);
     UART_SendString("ARW9679 initialization successful\r\n");
  }
  else
  {
     UART_SendString("ARW9679 initialization failed\r\n");
  }
  // 启动串口接收中断
  HAL_UART_Receive_IT(&huart1, uart_rx_buffer, 1);
  
  // 发送欢迎信息
  UART_SendString("\r\n=== ARW9679 UART Control Interface ===\r\n");
  UART_SendString("Type 'help' for available commands.\r\n");
  UART_SendString("Ready for commands...\r\n");		
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    // 检查是否有串口命令需要处理
    if (uart_cmd_ready)
    {
        ProcessUARTCommand((char*)uart_cmd_buffer);
        uart_cmd_ready = 0;
    }
       SendShortFrame(&SoftSPI1,0, 0x1234, 0xABCD);
		HAL_Delay(100);
    SendShortFrame(&SoftSPI1, 1,0x1234, 0xABCD);
		HAL_Delay(100); 
    // 主循环延时
    HAL_Delay(10);		
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 12;
  RCC_OscInitStruct.PLL.PLLN = 96;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 8;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_3) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
/**
 * @brief 串口接收中断回调函数
 * @param huart: UART句柄指针
 * @retval None
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1)
    {
        // 检查是否为换行符或回车符
        if (uart_rx_buffer[0] == '\r' || uart_rx_buffer[0] == '\n')
        {
            if (uart_rx_index > 0)
            {
                uart_cmd_buffer[uart_rx_index] = '\0';
                uart_cmd_ready = 1;
                uart_rx_index = 0;
            }
        }
        else if (uart_rx_index < UART_CMD_BUFFER_SIZE - 1)
        {
            uart_cmd_buffer[uart_rx_index++] = uart_rx_buffer[0];
        }
        
        // 重新启动接收
        HAL_UART_Receive_IT(&huart1, uart_rx_buffer, 1);
    }
}
//串口命令解析和处理函数
void ProcessUARTCommand(char* cmd)
{
    char* token;
//    char response[256];
    
    // 移除换行符
    char* newline = strchr(cmd, '\r');
    if (newline) *newline = '\0';
    newline = strchr(cmd, '\n');
    if (newline) *newline = '\0';
    
    UART_SendFormattedString("Received command: %s\r\n", cmd);
    
    // Short帧读取命令: short_r_0x1234
    if (strncmp(cmd, "short_r_0x", 10) == 0)
    {
        uint16_t address = (uint16_t)strtol(cmd + 10, NULL, 16);
        uint16_t data = ARW9679_ReadShortFrame(&SoftSPI1, address);
        UART_SendFormattedString("Short Read - Address: 0x%04X, Data: 0x%04X\r\n", address, data);
    }
    // Short帧写入命令: short_w_0x1234_0x5678
    else if (strncmp(cmd, "short_w_0x", 10) == 0)
    {
        token = strtok(cmd + 10, "_");
        if (token != NULL)
        {
            uint16_t address = (uint16_t)strtol(token, NULL, 16);
            token = strtok(NULL, "_");
            if (token != NULL && strncmp(token, "0x", 2) == 0)
            {
                uint16_t data = (uint16_t)strtol(token, NULL, 16);
                ARW9679_WriteShortFrame(&SoftSPI1, address, data);
                UART_SendFormattedString("Short Write - Address: 0x%04X, Data: 0x%04X\r\n", address, data);
            }
            else
            {
                UART_SendString("Error: Invalid short write format. Use: short_w_0xADDR_0xDATA\r\n");
            }
        }
    }
    // Long帧读取命令: long_r_0x480
    else if (strncmp(cmd, "long_r_0x", 9) == 0)
    {
        uint16_t address = (uint16_t)strtol(cmd + 9, NULL, 16);
        uint8_t data[29];
        ARW9679_ReadLongFrame(&SoftSPI1, address, data);
        
        UART_SendFormattedString("Long Read - Address: 0x%04X, Data: ", address);
        for (int i = 0; i < 29; i++)
        {
            UART_SendFormattedString("%02X ", data[i]);
        }
        UART_SendString("\r\n");
    }
    // Long帧写入命令: long_w_0x480_DATA (DATA为58个十六进制字符)
    else if (strncmp(cmd, "long_w_0x", 9) == 0)
    {
        token = strtok(cmd + 9, "_");
        if (token != NULL)
        {
            uint16_t address = (uint16_t)strtol(token, NULL, 16);
            token = strtok(NULL, "_");
            if (token != NULL && strlen(token) == 58) // 29字节 = 58个十六进制字符
            {
                uint8_t data[29];
                for (int i = 0; i < 29; i++)
                {
                    char hex_byte[3] = {token[i*2], token[i*2+1], '\0'};
                    data[i] = (uint8_t)strtol(hex_byte, NULL, 16);
                }
                ARW9679_WriteLongFrame(&SoftSPI1, address, data);
                UART_SendFormattedString("Long Write - Address: 0x%04X, 29 bytes written\r\n", address);
            }
            else
            {
                UART_SendString("Error: Invalid long write format. Use: long_w_0xADDR_58HexChars\r\n");
            }
        }
    }
    // 温度读取命令: read_temp
    else if (strcmp(cmd, "read_temp") == 0)
    {
        int16_t temp = ARW9679_ReadTemperatureDirect(&SoftSPI1);
        if (temp != -999)
        {
            UART_SendFormattedString("Temperature: %d°C\r\n", temp);
        }
        else
        {
            UART_SendString("Error: Temperature read failed\r\n");
        }
    }
    // 温度读取命令: read_temp
    else if (strcmp(cmd, "switch_short") == 0)
    {
			ARW9679_SwitchToShortFrame(&SoftSPI1);
    }	
    else if (strcmp(cmd, "switch_long") == 0)
    {
			ARW9679_SwitchToLongFrame(&SoftSPI1);
    }		
    else if (strcmp(cmd, "init") == 0)
    {
			ARW9679_Initialize(&SoftSPI1);
    }		
    else if (strcmp(cmd, "tddinit") == 0)
    {
			ARW9679_TDD_Initialize(&SoftSPI1);
    }		
    else if (strcmp(cmd, "reset") == 0)
    {
			ARW9679_TimingReset(&SoftSPI1);
    }			
    // 帮助命令
    else if (strcmp(cmd, "help") == 0)
    {
        UART_SendString("Available commands:\r\n");
        UART_SendString("  short_r_0xADDR        - Read short frame from address\r\n");
        UART_SendString("  short_w_0xADDR_0xDATA - Write short frame to address\r\n");
        UART_SendString("  long_r_0xADDR         - Read long frame from address\r\n");
        UART_SendString("  long_w_0xADDR_DATA    - Write long frame (DATA=58 hex chars)\r\n");
        UART_SendString("  read_temp             - Read temperature\r\n");
        UART_SendString("  switch_short          - switch_short\r\n");
        UART_SendString("  switch_long           - switch_long\r\n");
        UART_SendString("  init             		 - init\r\n");
        UART_SendString("  tddinit             	 - tddinit\r\n");			
        UART_SendString("  reset             		 - reset\r\n");			
        UART_SendString("  help                  - Show this help\r\n");
    }
    else
    {
        UART_SendString("Error: Unknown command. Type 'help' for available commands.\r\n");
    }
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
