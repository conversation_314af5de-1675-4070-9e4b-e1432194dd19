--cpu=Cortex-M4.fp.sp
"1100\startup_stm32f411xe.o"
"1100\softspi.o"
"1100\main.o"
"1100\gpio.o"
"1100\spi.o"
"1100\tim.o"
"1100\usart.o"
"1100\stm32f4xx_it.o"
"1100\stm32f4xx_hal_msp.o"
"1100\stm32f4xx_hal_spi.o"
"1100\stm32f4xx_hal_rcc.o"
"1100\stm32f4xx_hal_rcc_ex.o"
"1100\stm32f4xx_hal_flash.o"
"1100\stm32f4xx_hal_flash_ex.o"
"1100\stm32f4xx_hal_flash_ramfunc.o"
"1100\stm32f4xx_hal_gpio.o"
"1100\stm32f4xx_hal_dma_ex.o"
"1100\stm32f4xx_hal_dma.o"
"1100\stm32f4xx_hal_pwr.o"
"1100\stm32f4xx_hal_pwr_ex.o"
"1100\stm32f4xx_hal_cortex.o"
"1100\stm32f4xx_hal.o"
"1100\stm32f4xx_hal_exti.o"
"1100\stm32f4xx_hal_tim.o"
"1100\stm32f4xx_hal_tim_ex.o"
"1100\stm32f4xx_hal_uart.o"
"1100\system_stm32f4xx.o"
--strict --scatter "1100\1100.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "1100.map" -o 1100\1100.axf